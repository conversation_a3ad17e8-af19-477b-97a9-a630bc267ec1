/**
 * Client-side mTLS agent utilities
 */

import { Agent as HTTPSAgent } from "https";
import { constants as cryptoConstants } from "crypto";
import { MTLSClientOptions, MTLSAgentResult, MTLSAgentOptions } from "../types";
import {
  loadCertificate,
  loadCACertificate,
  validateCertificate,
} from "../certificates";

/**
 * Create an HTTPS agent with mTLS support
 *
 * @param options mTLS client options
 * @returns Agent result with the created agent and mTLS status
 */
export async function createMTLSAgent(
  options: MTLSClientOptions = {}
): Promise<MTLSAgentResult> {
  const {
    enableMTLS = process.env.ENABLE_MTLS === "true" ||
      process.env.ENABLE_MTLS === "1",
    verifyServer = process.env.MTLS_VERIFY_SERVER !== "false" &&
      process.env.MTLS_VERIFY_SERVER !== "0",
    requireClientCert = process.env.MTLS_REQUIRE_CLIENT_CERT !== "false" &&
      process.env.MTLS_REQUIRE_CLIENT_CERT !== "0",
    environment = (process.env.ENVIRONMENT as any) || "local",
    debug = process.env.DEBUG_MTLS === "true" || process.env.DEBUG_MTLS === "1",
  } = options;

  // If mTLS is not enabled, return a regular HTTPS agent
  if (!enableMTLS) {
    if (debug) {
      console.log("🔑 mTLS is disabled, using regular HTTPS agent");
    }
    return {
      agent: new HTTPSAgent({ rejectUnauthorized: verifyServer }),
      mtlsEnabled: false,
    };
  }

  if (debug) {
    console.log("🔐 mTLS is enabled");
    console.log(
      "🔑 mTLS server verification:",
      verifyServer ? "enabled" : "disabled"
    );
  }

  try {
    let clientCert: string | null = null;
    let clientKey: string | null = null;

    // Load client certificate and key only if required
    if (requireClientCert) {
      clientCert = loadCertificate("client", "cert", environment);
      clientKey = loadCertificate("client", "key", environment);

      if (!clientCert || !clientKey) {
        throw new Error("Client certificate or key not found");
      }

      if (
        !validateCertificate(clientCert, "cert") ||
        !validateCertificate(clientKey, "key")
      ) {
        throw new Error("Invalid client certificate or key");
      }

      if (debug) {
        console.log("✅ Loaded client certificate and key");
      }
    } else {
      if (debug) {
        console.log(
          "ℹ️  Client certificates not required - using server-only mTLS"
        );
      }
    }

    // Setup HTTPS agent options
    const agentOptions: MTLSAgentOptions = {
      rejectUnauthorized: verifyServer,
    };

    // Add client certificate and key only if they exist
    if (clientCert && clientKey) {
      agentOptions.cert = clientCert;
      agentOptions.key = clientKey;
    }

    // If server verification is enabled, load CA certificate
    if (verifyServer) {
      const caCert = loadCACertificate(environment);
      if (caCert) {
        if (validateCertificate(caCert, "cert")) {
          agentOptions.ca = caCert;
          if (debug) {
            console.log("✅ Loaded CA certificate for server verification");
          }
        } else {
          console.warn(
            "⚠️ Invalid CA certificate, using system CA certificates"
          );
        }
      } else {
        if (debug) {
          console.log(
            "⚠️ CA certificate not found, using system CA certificates"
          );
        }
      }
    }

    if (debug) {
      console.log("🔐 Creating HTTPS agent with mTLS options");
    }

    // Create HTTPS agent
    return {
      agent: new HTTPSAgent({
        ...agentOptions,
        secureOptions: cryptoConstants.SSL_OP_NO_TLSv1_3,
      }),
      mtlsEnabled: true,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("❌ Error setting up mTLS agent:", errorMessage);
    console.log("⚠️ Falling back to regular HTTPS agent");
    return {
      agent: new HTTPSAgent({ rejectUnauthorized: verifyServer }),
      mtlsEnabled: false,
    };
  }
}
