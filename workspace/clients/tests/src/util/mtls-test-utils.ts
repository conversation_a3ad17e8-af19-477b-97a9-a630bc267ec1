/**
 * mTLS Testing Utilities
 *
 * This module provides utilities for testing mTLS functionality across
 * different test suites and scenarios.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

export interface MTLSTestConfig {
  certPaths: {
    ca: string;
    serverCert: string;
    serverKey: string;
    clientCert: string;
    clientKey: string;
  };
  workers: {
    [key: string]: {
      name: string;
      healthEndpoint: string;
      processEndpoint: string;
    };
  };
  timeout: number;
}

export interface CertificateInfo {
  subject: string;
  issuer: string;
  validFrom: Date;
  validTo: Date;
  fingerprint: string;
  isValid: boolean;
}

/**
 * Default mTLS test configuration
 */
export const DEFAULT_MTLS_CONFIG: MTLSTestConfig = {
  certPaths: {
    ca: 'workspace/resources/mtls/ca/ca-cert.pem',
    serverCert: 'workspace/resources/mtls/server/server-cert.pem',
    serverKey: 'workspace/resources/mtls/server/server-key.pem',
    clientCert: 'workspace/resources/mtls/client/client-cert.pem',
    clientKey: 'workspace/resources/mtls/client/client-key.pem'
  },
  workers: {
    pyannote: {
      name: 'pyannote',
      healthEndpoint: '/health',
      processEndpoint: '/process'
    },
    openParse: {
      name: 'open-parse',
      healthEndpoint: '/health',
      processEndpoint: '/parse'
    }
  },
  timeout: 30000
};

/**
 * Check if all required mTLS certificate files exist
 * @param config mTLS test configuration
 * @returns Object with existence status for each certificate
 */
export function checkCertificateFiles(config: MTLSTestConfig = DEFAULT_MTLS_CONFIG): {
  [key: string]: { exists: boolean; path: string; size?: number };
} {
  const workspaceRoot = process.cwd();
  const results: { [key: string]: { exists: boolean; path: string; size?: number } } = {};

  for (const [certType, certPath] of Object.entries(config.certPaths)) {
    const fullPath = path.join(workspaceRoot, certPath);
    const exists = fs.existsSync(fullPath);
    
    results[certType] = {
      exists,
      path: fullPath,
      ...(exists && { size: fs.statSync(fullPath).size })
    };
  }

  return results;
}

/**
 * Validate certificate format and basic content
 * @param certPath Path to certificate file
 * @returns Validation result
 */
export function validateCertificateFormat(certPath: string): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    if (!fs.existsSync(certPath)) {
      errors.push(`Certificate file does not exist: ${certPath}`);
      return { isValid: false, errors, warnings };
    }

    const certContent = fs.readFileSync(certPath, 'utf8');

    // Check for PEM format
    if (!certContent.includes('-----BEGIN CERTIFICATE-----')) {
      errors.push('Certificate does not contain BEGIN CERTIFICATE marker');
    }

    if (!certContent.includes('-----END CERTIFICATE-----')) {
      errors.push('Certificate does not contain END CERTIFICATE marker');
    }

    // Check for private key format (for key files)
    if (certPath.includes('key') || certPath.includes('private')) {
      if (!certContent.includes('-----BEGIN PRIVATE KEY-----') && 
          !certContent.includes('-----BEGIN RSA PRIVATE KEY-----')) {
        warnings.push('Key file does not contain expected private key markers');
      }
    }

    // Basic content validation
    if (certContent.trim().length < 100) {
      warnings.push('Certificate content seems unusually short');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  } catch (error) {
    errors.push(`Error reading certificate file: ${error.message}`);
    return { isValid: false, errors, warnings };
  }
}

/**
 * Extract certificate information (basic parsing)
 * @param certPath Path to certificate file
 * @returns Certificate information
 */
export function extractCertificateInfo(certPath: string): CertificateInfo | null {
  try {
    if (!fs.existsSync(certPath)) {
      return null;
    }

    const certContent = fs.readFileSync(certPath, 'utf8');
    
    // Create a simple fingerprint from the certificate content
    const fingerprint = crypto
      .createHash('sha256')
      .update(certContent)
      .digest('hex')
      .toUpperCase()
      .match(/.{2}/g)
      ?.join(':') || '';

    // For now, return basic info (in a real implementation, you'd parse the X.509 certificate)
    return {
      subject: 'CN=Test Certificate', // Placeholder
      issuer: 'CN=Test CA', // Placeholder
      validFrom: new Date(Date.now() - 86400000), // Yesterday
      validTo: new Date(Date.now() + 31536000000), // Next year
      fingerprint,
      isValid: true
    };
  } catch (error) {
    return null;
  }
}

/**
 * Test mTLS connection to a service
 * @param url Service URL
 * @param config mTLS configuration
 * @returns Connection test result
 */
export async function testMTLSConnection(
  url: string,
  config: MTLSTestConfig = DEFAULT_MTLS_CONFIG
): Promise<{
  success: boolean;
  error?: string;
  responseStatus?: number;
  usedMTLS: boolean;
}> {
  try {
    // Import serviceFetch dynamically
    const { serviceFetch } = await import('../../../resources/server-utils/dist/http-request/service-fetch');
    
    const response = await serviceFetch(url);
    
    return {
      success: true,
      responseStatus: response.status,
      usedMTLS: isInternalUrl(url)
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      usedMTLS: isInternalUrl(url)
    };
  }
}

/**
 * Check if a URL is considered internal (should use mTLS)
 * @param url URL to check
 * @returns True if URL is internal
 */
export function isInternalUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    
    // Internal hostnames that should use mTLS
    const internalHosts = [
      'localhost',
      '127.0.0.1',
      '0.0.0.0',
      'api.divinci.ai',
      'staging-api.divinci.ai',
      'dev-api.divinci.ai'
    ];
    
    return internalHosts.some(host => hostname.includes(host)) ||
           hostname.endsWith('.internal') ||
           hostname.endsWith('.local');
  } catch {
    return false;
  }
}

/**
 * Validate worker mTLS configuration
 * @param workerName Name of the worker (pyannote, open-parse)
 * @returns Validation result
 */
export function validateWorkerMTLSConfig(workerName: string): {
  hasUtilsFile: boolean;
  utilsPath: string;
  hasMTLSFunctions: boolean;
  missingFunctions: string[];
} {
  const workspaceRoot = process.cwd();
  const utilsPath = path.join(workspaceRoot, `workspace/workers/${workerName}/mtls_utils.py`);
  
  const result = {
    hasUtilsFile: false,
    utilsPath,
    hasMTLSFunctions: false,
    missingFunctions: [] as string[]
  };

  if (!fs.existsSync(utilsPath)) {
    return result;
  }

  result.hasUtilsFile = true;

  try {
    const utilsContent = fs.readFileSync(utilsPath, 'utf8');
    
    const requiredFunctions = [
      'is_mtls_enabled',
      'get_mtls_config',
      'create_mtls_session',
      'configure_flask_mtls'
    ];

    const missingFunctions = requiredFunctions.filter(func => 
      !utilsContent.includes(`def ${func}`)
    );

    result.hasMTLSFunctions = missingFunctions.length === 0;
    result.missingFunctions = missingFunctions;

    return result;
  } catch (error) {
    return result;
  }
}

/**
 * Create a mock mTLS environment for testing
 * @returns Environment variables for mTLS testing
 */
export function createMockMTLSEnvironment(config: MTLSTestConfig = DEFAULT_MTLS_CONFIG): {
  [key: string]: string;
} {
  const workspaceRoot = process.cwd();
  
  return {
    MTLS_ENABLED: 'true',
    MTLS_CA_CERT: path.join(workspaceRoot, config.certPaths.ca),
    MTLS_SERVER_CERT: path.join(workspaceRoot, config.certPaths.serverCert),
    MTLS_SERVER_KEY: path.join(workspaceRoot, config.certPaths.serverKey),
    MTLS_CLIENT_CERT: path.join(workspaceRoot, config.certPaths.clientCert),
    MTLS_CLIENT_KEY: path.join(workspaceRoot, config.certPaths.clientKey),
    DEBUG_MTLS: 'true'
  };
}

/**
 * Generate a test report for mTLS configuration
 * @param config mTLS test configuration
 * @returns Test report
 */
export async function generateMTLSTestReport(config: MTLSTestConfig = DEFAULT_MTLS_CONFIG): Promise<{
  certificates: { [key: string]: any };
  workers: { [key: string]: any };
  connectivity: { [key: string]: any };
  summary: {
    totalTests: number;
    passed: number;
    failed: number;
    warnings: number;
  };
}> {
  const report = {
    certificates: {},
    workers: {},
    connectivity: {},
    summary: {
      totalTests: 0,
      passed: 0,
      failed: 0,
      warnings: 0
    }
  };

  // Test certificates
  const certFiles = checkCertificateFiles(config);
  for (const [certType, certInfo] of Object.entries(certFiles)) {
    const validation = validateCertificateFormat(certInfo.path);
    report.certificates[certType] = {
      ...certInfo,
      ...validation
    };
    
    report.summary.totalTests++;
    if (validation.isValid) {
      report.summary.passed++;
    } else {
      report.summary.failed++;
    }
    
    report.summary.warnings += validation.warnings.length;
  }

  // Test workers
  for (const [workerName, workerConfig] of Object.entries(config.workers)) {
    const workerValidation = validateWorkerMTLSConfig(workerName);
    report.workers[workerName] = workerValidation;
    
    report.summary.totalTests++;
    if (workerValidation.hasMTLSFunctions) {
      report.summary.passed++;
    } else {
      report.summary.failed++;
    }
  }

  return report;
}
