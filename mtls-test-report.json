{"certificates": {"ca": {"path": "/workspaces/server/workspace/resources/mtls/ca/ca-cert.pem", "exists": true, "size": 1915, "format": "PEM Certificate", "valid": true, "errors": [], "warnings": []}, "serverCert": {"path": "/workspaces/server/workspace/resources/mtls/server/server-cert.pem", "exists": true, "size": 1789, "format": "PEM Certificate", "valid": true, "errors": [], "warnings": []}, "serverKey": {"path": "/workspaces/server/workspace/resources/mtls/server/server-key.pem", "exists": true, "size": 1704, "format": "PEM Private Key", "valid": true, "errors": [], "warnings": []}}, "workers": {"audio-speaker-diarization@pyannote": {"name": "audio-speaker-diarization@pyannote", "hasUtilsFile": true, "utilsPath": "/workspaces/server/workspace/workers/audio-speaker-diarization@pyannote/mtls_utils.py", "hasMTLSFunctions": true, "missingFunctions": [], "valid": true}, "open-parse": {"name": "open-parse", "hasUtilsFile": true, "utilsPath": "/workspaces/server/workspace/workers/open-parse/mtls_utils.py", "hasMTLSFunctions": true, "missingFunctions": [], "valid": true}}, "connectivity": {"localApi": {"endpoint": "https://localhost:3000/health", "reachable": false, "responseTime": 37, "statusCode": 0, "error": "", "usedMTLS": true}, "devApi": {"endpoint": "https://dev-api.divinci.ai/health", "reachable": false, "responseTime": 36, "statusCode": 0, "error": "getaddrinfo ENOTFOUND dev-api.divinci.ai", "usedMTLS": true}, "stagingApi": {"endpoint": "https://staging-api.divinci.ai/health", "reachable": false, "responseTime": 30, "statusCode": 0, "error": "getaddrinfo ENOTFOUND staging-api.divinci.ai", "usedMTLS": true}}, "summary": {"totalTests": 12, "passed": 6, "failed": 6, "warnings": 0}, "serviceFetch": {"moduleLoaded": true, "internalCallTest": {"success": false, "error": "fetch failed"}, "externalCallTest": {"success": true, "error": null}, "valid": true}}