#!/bin/bash

# Generate mTLS Certificates for Development/Testing
# This script creates a complete set of mTLS certificates for testing purposes

set -e

echo "🔐 Generating mTLS Certificates for Development/Testing"
echo "======================================================="

# Configuration
CERT_BASE_DIR="workspace/resources/mtls"
CA_DIR="${CERT_BASE_DIR}/ca"
SERVER_DIR="${CERT_BASE_DIR}/server"
# CLIENT_DIR="${CERT_BASE_DIR}/client"  # Not needed for server-only mTLS

# Certificate validity (days)
CA_VALIDITY=3650  # 10 years
CERT_VALIDITY=365 # 1 year

# Certificate subjects
CA_SUBJECT="/CN=Divinci Test CA/O=Divinci AI/C=US"
SERVER_SUBJECT="/CN=api.divinci.ai/O=Divinci AI/C=US"
CLIENT_SUBJECT="/CN=client.divinci.ai/O=Divinci AI/C=US"

echo "📁 Creating directory structure..."

# Create directory structure
mkdir -p "${CA_DIR}"
mkdir -p "${SERVER_DIR}"
# mkdir -p "${CLIENT_DIR}"  # Not needed for server-only mTLS

echo "🏛️  Step 1: Generating Certificate Authority (CA)"
echo "------------------------------------------------"

# Generate CA private key
echo "  🔑 Generating CA private key..."
openssl genrsa -out "${CA_DIR}/ca-key.pem" 4096

# Generate CA certificate
echo "  📜 Generating CA certificate..."
openssl req -new -x509 \
    -key "${CA_DIR}/ca-key.pem" \
    -out "${CA_DIR}/ca-cert.pem" \
    -days ${CA_VALIDITY} \
    -subj "${CA_SUBJECT}"

echo "  ✅ CA certificate generated successfully"

echo ""
echo "🖥️  Step 2: Generating Server Certificate"
echo "----------------------------------------"

# Generate server private key
echo "  🔑 Generating server private key..."
openssl genrsa -out "${SERVER_DIR}/server-key.pem" 2048

# Generate server certificate signing request (CSR)
echo "  📝 Generating server CSR..."
openssl req -new \
    -key "${SERVER_DIR}/server-key.pem" \
    -out "${SERVER_DIR}/server.csr" \
    -subj "${SERVER_SUBJECT}"

# Create server certificate extensions file
echo "  📋 Creating server certificate extensions..."
cat > "${SERVER_DIR}/server.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = api.divinci.ai
DNS.2 = *.divinci.ai
DNS.3 = localhost
DNS.4 = *.localhost
DNS.5 = staging-api.divinci.ai
DNS.6 = dev-api.divinci.ai
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Sign server certificate with CA
echo "  ✍️  Signing server certificate with CA..."
openssl x509 -req \
    -in "${SERVER_DIR}/server.csr" \
    -CA "${CA_DIR}/ca-cert.pem" \
    -CAkey "${CA_DIR}/ca-key.pem" \
    -CAcreateserial \
    -out "${SERVER_DIR}/server-cert.pem" \
    -days ${CERT_VALIDITY} \
    -extfile "${SERVER_DIR}/server.ext"

echo "  ✅ Server certificate generated successfully"

echo ""
echo "👤 Step 3: Skipping Client Certificate Generation"
echo "------------------------------------------------"
echo "  ℹ️  Client certificates not needed for server-only mTLS"
echo "  ℹ️  Workers will verify server certificates using CA cert only"

echo ""
echo "🔍 Step 4: Verifying Certificates"
echo "--------------------------------"

# Verify server certificate
echo "  🖥️  Verifying server certificate..."
if openssl verify -CAfile "${CA_DIR}/ca-cert.pem" "${SERVER_DIR}/server-cert.pem"; then
    echo "  ✅ Server certificate verification passed"
else
    echo "  ❌ Server certificate verification failed"
    exit 1
fi

# Skip client certificate verification - not generated for server-only mTLS
echo "  👤 Skipping client certificate verification (not needed)"

echo ""
echo "🔒 Step 5: Setting File Permissions"
echo "----------------------------------"

# Set appropriate permissions
chmod 644 "${CA_DIR}/ca-cert.pem"
chmod 600 "${CA_DIR}/ca-key.pem"
chmod 644 "${SERVER_DIR}/server-cert.pem"
chmod 600 "${SERVER_DIR}/server-key.pem"
# Client certificate permissions not needed

echo "  ✅ File permissions set correctly"

echo ""
echo "📋 Step 6: Cleaning Up Temporary Files"
echo "-------------------------------------"

# Remove temporary files
rm -f "${SERVER_DIR}/server.csr" "${SERVER_DIR}/server.ext"
# Client certificate temporary files not generated
rm -f "${CA_DIR}/ca-cert.srl"

echo "  ✅ Temporary files cleaned up"

echo ""
echo "📊 Certificate Generation Summary"
echo "================================"
echo "📁 Certificate directory: ${CERT_BASE_DIR}"
echo ""
echo "🏛️  Certificate Authority:"
echo "   📜 Certificate: ${CA_DIR}/ca-cert.pem"
echo "   🔑 Private Key: ${CA_DIR}/ca-key.pem"
echo ""
echo "🖥️  Server Certificate:"
echo "   📜 Certificate: ${SERVER_DIR}/server-cert.pem"
echo "   🔑 Private Key: ${SERVER_DIR}/server-key.pem"
echo ""
echo "👤 Client Certificate:"
echo "   ℹ️  Not generated (server-only mTLS configuration)"
echo ""

echo "🎉 mTLS certificates generated successfully!"
echo ""
echo "📝 Next Steps:"
echo "1. Set environment variables for mTLS:"
echo "   export MTLS_ENABLED=true"
echo "   export MTLS_CA_CERT=$(pwd)/${CA_DIR}/ca-cert.pem"
echo "   export MTLS_SERVER_CERT=$(pwd)/${SERVER_DIR}/server-cert.pem"
echo "   export MTLS_SERVER_KEY=$(pwd)/${SERVER_DIR}/server-key.pem"
echo "   # Client certificate environment variables not needed for server-only mTLS"
echo ""
echo "2. Test the certificates with:"
echo "   node test-mtls-deployment.js"
echo ""
echo "3. Run the mTLS integration tests:"
echo "   cd workspace/clients/tests && npm test -- mtls"
